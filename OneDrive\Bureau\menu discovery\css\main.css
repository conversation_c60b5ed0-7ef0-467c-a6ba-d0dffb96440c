/* ===================================
   MODERN RESTAURANT MENU CSS
   ===================================
   
   Architecture:
   1. CSS Custom Properties (Design Tokens)
   2. Reset & Base Styles
   3. Layout Components
   4. Menu Components
   5. Responsive Design
   6. Print Styles
   7. Accessibility Features
   
   ================================= */

/* ===================================
   1. CSS CUSTOM PROPERTIES (Design Tokens)
   ================================= */
:root {
  /* Colors */
  --color-primary: #e74c3c;
  --color-primary-light: #ec7063;
  --color-primary-dark: #c0392b;
  
  --color-secondary: #27ae60;
  --color-secondary-light: #58d68d;
  --color-secondary-dark: #229954;
  
  --color-neutral-900: #2c3e50;
  --color-neutral-700: #34495e;
  --color-neutral-500: #7f8c8d;
  --color-neutral-300: #bdc3c7;
  --color-neutral-100: #ecf0f1;
  --color-neutral-50: #f8f9fa;
  
  --color-background: #fffdf6;
  --color-surface: #ffffff;
  --color-text: #2c3e50;
  --color-text-muted: #7f8c8d;
  
  /* Typography */
  --font-family-primary: 'Playfair Display', Georgia, serif;
  --font-family-secondary: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  
  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;
  
  /* Spacing */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */
  
  /* Layout */
  --container-max-width: 148mm; /* A5 width for print */
  --border-radius-sm: 0.25rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* ===================================
   2. RESET & BASE STYLES
   ================================= */
*,
*::before,
*::after {
  box-sizing: border-box;
}

* {
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-secondary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  padding: var(--space-md);
  margin: 0 auto;
  max-width: var(--container-max-width);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===================================
   3. LAYOUT COMPONENTS
   ================================= */
.menu-container {
  background: var(--color-surface);
  padding: var(--space-xl);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.menu-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--color-primary),
    var(--color-primary-light),
    var(--color-primary)
  );
}

/* ===================================
   4. MENU COMPONENTS
   ================================= */

/* Header */
.menu-header {
  text-align: center;
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-xl);
  border-bottom: 3px solid var(--color-primary);
  position: relative;
}

.menu-title {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-md);
  letter-spacing: 0.05em;
}

.header-decoration {
  width: 80px;
  height: 3px;
  background: linear-gradient(
    90deg,
    transparent,
    var(--color-primary),
    transparent
  );
  margin: var(--space-md) auto 0;
}

/* Sections */
.menu-section {
  margin-bottom: var(--space-2xl);
}

.menu-section:last-of-type {
  margin-bottom: 0;
}

.section-header {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-primary);
  margin-bottom: var(--space-md);
  padding: var(--space-sm) 0;
  border-bottom: 2px solid var(--color-neutral-100);
  position: relative;
}

.section-header::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--color-primary);
}

/* Menu Items */
.menu-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
}

.menu-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-xs) 0;
  border-bottom: 1px dotted var(--color-neutral-300);
  transition: var(--transition-fast);
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: var(--color-neutral-50);
  padding: var(--space-sm);
  margin: calc(-1 * var(--space-sm));
  border-radius: var(--border-radius-sm);
}

.item-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-neutral-900);
  flex: 1;
}

.item-price {
  font-weight: var(--font-weight-semibold);
  color: var(--color-secondary);
  white-space: nowrap;
  margin-left: var(--space-md);
}

/* Pizza Items (Special Layout) */
.pizza-item {
  flex-direction: column;
  align-items: flex-start;
  padding: var(--space-md) 0;
  gap: var(--space-xs);
}

.pizza-header {
  width: 100%;
}

.pizza-name {
  font-family: var(--font-family-primary);
  font-weight: var(--font-weight-semibold);
  color: var(--color-neutral-900);
  margin-bottom: var(--space-xs);
  font-size: var(--font-size-base);
}

.pizza-description {
  font-size: var(--font-size-xs);
  color: var(--color-text-muted);
  font-style: italic;
  margin-bottom: var(--space-sm);
}

.pizza-prices {
  display: flex;
  justify-content: space-between;
  width: 100%;
  gap: var(--space-md);
}

.pizza-size {
  font-size: var(--font-size-sm);
  color: var(--color-text-muted);
}

.pizza-size strong {
  color: var(--color-secondary);
  font-weight: var(--font-weight-semibold);
}

/* Footer */
.menu-footer {
  margin-top: var(--space-2xl);
  padding-top: var(--space-xl);
  border-top: 2px solid var(--color-primary);
  text-align: center;
}

.footer-note {
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  font-style: italic;
}

/* ===================================
   5. RESPONSIVE DESIGN
   ================================= */

/* Tablet Styles */
@media screen and (max-width: 768px) {
  :root {
    --font-size-2xl: 1.375rem; /* 22px */
    --font-size-lg: 1rem;      /* 16px */
  }

  body {
    padding: var(--space-sm);
  }

  .menu-container {
    padding: var(--space-lg);
  }

  .menu-header {
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
  }

  .menu-section {
    margin-bottom: var(--space-xl);
  }
}

/* Mobile Styles */
@media screen and (max-width: 480px) {
  :root {
    --font-size-2xl: 1.25rem;  /* 20px */
    --font-size-lg: 0.875rem;  /* 14px */
    --space-xl: 1.5rem;        /* 24px */
    --space-2xl: 2rem;         /* 32px */
  }

  body {
    padding: var(--space-xs);
  }

  .menu-container {
    padding: var(--space-md);
  }

  .pizza-prices {
    flex-direction: column;
    gap: var(--space-xs);
  }

  .pizza-size {
    text-align: left;
  }
}

/* ===================================
   6. PRINT STYLES
   ================================= */
@media print {
  :root {
    --color-primary: #000000;
    --color-secondary: #000000;
    --color-neutral-900: #000000;
    --color-text: #000000;
    --color-text-muted: #555555;
    --color-background: #ffffff;
    --color-surface: #ffffff;
  }

  @page {
    size: A5 portrait;
    margin: 10mm;
  }

  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  body {
    background-color: white !important;
    padding: 0;
    font-size: 10pt;
    line-height: 1.3;
  }

  .menu-container {
    box-shadow: none !important;
    border: 1px solid #ddd;
    padding: 15mm;
    page-break-inside: avoid;
  }

  .menu-container::before {
    display: none;
  }

  .menu-title {
    font-size: 18pt;
    margin-bottom: 8pt;
  }

  .section-header {
    font-size: 12pt;
    page-break-after: avoid;
    margin-bottom: 6pt;
  }

  .menu-item {
    page-break-inside: avoid;
    margin-bottom: 3pt;
    padding: 2pt 0;
  }

  .menu-item:hover {
    background-color: transparent !important;
    padding: 2pt 0;
    margin: 0;
  }

  .pizza-item {
    padding: 4pt 0;
    margin-bottom: 6pt;
  }

  .pizza-name {
    font-size: 10pt;
  }

  .pizza-description {
    font-size: 8pt;
  }

  .pizza-size {
    font-size: 9pt;
  }

  .footer-note {
    font-size: 8pt;
    page-break-inside: avoid;
  }
}

/* ===================================
   7. ACCESSIBILITY FEATURES
   ================================= */

/* Focus Styles */
.menu-item:focus-within {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
  border-radius: var(--border-radius-sm);
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #000000;
    --color-secondary: #000000;
    --color-neutral-900: #000000;
    --color-text: #000000;
    --color-text-muted: #333333;
  }

  .menu-container {
    border: 2px solid #000000;
  }

  .menu-item {
    border-bottom: 1px solid #000000;
  }
}

/* ===================================
   8. UTILITY CLASSES
   ================================= */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.no-print {
  @media print {
    display: none !important;
  }
}

.print-only {
  display: none;

  @media print {
    display: block;
  }
}
