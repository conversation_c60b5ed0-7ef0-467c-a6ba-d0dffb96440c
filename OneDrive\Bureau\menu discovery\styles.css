/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f8f8;
}

/* Menu Container */
.menu-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    padding: 40px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Header Styles */
.menu-header {
    text-align: center;
    margin-bottom: 40px;
    border-bottom: 2px solid #d4af37;
    padding-bottom: 30px;
}

.restaurant-name {
    font-family: 'Playfair Display', serif;
    font-size: 3rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 10px;
    letter-spacing: 2px;
}

.restaurant-tagline {
    font-size: 1.2rem;
    color: #7f8c8d;
    font-style: italic;
    margin-bottom: 20px;
}

.header-decoration {
    width: 100px;
    height: 3px;
    background: linear-gradient(to right, #d4af37, #f1c40f, #d4af37);
    margin: 0 auto;
}

/* Section Styles */
.menu-section {
    margin-bottom: 40px;
}

.section-title {
    font-family: 'Playfair Display', serif;
    font-size: 2rem;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: #d4af37;
}

/* Menu Items */
.menu-items {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.menu-item {
    border-bottom: 1px dotted #bdc3c7;
    padding-bottom: 20px;
}

.menu-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    margin-bottom: 8px;
}

.item-name {
    font-family: 'Playfair Display', serif;
    font-size: 1.3rem;
    color: #2c3e50;
    font-weight: 600;
    flex: 1;
}

.item-price {
    font-weight: 600;
    color: #d4af37;
    font-size: 1.2rem;
    margin-left: 20px;
}

.item-description {
    color: #7f8c8d;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-left: 0;
}

/* Footer */
.menu-footer {
    margin-top: 50px;
    padding-top: 30px;
    border-top: 2px solid #d4af37;
    text-align: center;
}

.footer-text {
    color: #7f8c8d;
    font-size: 0.9rem;
    margin-bottom: 10px;
    font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
    .menu-container {
        margin: 10px;
        padding: 20px;
    }
    
    .restaurant-name {
        font-size: 2.2rem;
    }
    
    .section-title {
        font-size: 1.6rem;
    }
    
    .item-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
    
    .item-price {
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .restaurant-name {
        font-size: 1.8rem;
    }
    
    .restaurant-tagline {
        font-size: 1rem;
    }
    
    .section-title {
        font-size: 1.4rem;
    }
    
    .item-name {
        font-size: 1.1rem;
    }
}

/* Print Styles */
@media print {
    body {
        background: white;
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .menu-container {
        box-shadow: none;
        margin: 0;
        padding: 20px;
        max-width: none;
    }
    
    .restaurant-name {
        font-size: 24pt;
        color: black;
    }
    
    .section-title {
        font-size: 16pt;
        color: black;
        page-break-after: avoid;
    }
    
    .menu-item {
        page-break-inside: avoid;
        margin-bottom: 15px;
    }
    
    .item-name {
        font-size: 12pt;
        color: black;
    }
    
    .item-price {
        color: black;
        font-size: 12pt;
    }
    
    .item-description {
        color: #555;
        font-size: 10pt;
    }
    
    .header-decoration {
        background: black;
    }
    
    .section-title::after {
        background: black;
    }
    
    .menu-footer {
        page-break-inside: avoid;
    }
}

/* Hover Effects for Interactive Elements */
.menu-item:hover {
    background-color: #f9f9f9;
    padding: 15px;
    margin: -15px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* Additional Visual Enhancements */
.menu-section:nth-child(even) {
    background-color: #fafafa;
    padding: 30px;
    margin: 0 -40px 40px -40px;
    border-radius: 8px;
}

@media (max-width: 768px) {
    .menu-section:nth-child(even) {
        margin: 0 -20px 40px -20px;
        padding: 20px;
    }
}
