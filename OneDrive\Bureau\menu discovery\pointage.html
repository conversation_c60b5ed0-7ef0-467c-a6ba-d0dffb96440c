<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DISCOVERY Living RESTO - Pointage</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: white;
            padding: 10px;
            font-size: 12px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border: 2px solid #000;
        }

        .header {
            text-align: center;
            padding: 10px;
            border-bottom: 2px solid #000;
            background-color: #f0f0f0;
        }

        .header h1 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .date-line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }

        .main-table {
            width: 100%;
            border-collapse: collapse;
            border: none;
        }

        .main-table th {
            background-color: #e0e0e0;
            border: 1px solid #000;
            padding: 5px;
            text-align: center;
            font-weight: bold;
            font-size: 11px;
        }

        .main-table td {
            border: 1px solid #000;
            padding: 3px 5px;
            text-align: left;
            font-size: 11px;
            height: 25px;
        }

        .commande-col {
            width: 200px;
            font-weight: bold;
        }

        .prix-col {
            width: 80px;
            text-align: center;
            font-weight: bold;
        }

        .input-col {
            width: 50px;
            text-align: center;
        }

        .input-col input {
            width: 45px;
            height: 20px;
            border: none;
            text-align: center;
            font-size: 11px;
            background: transparent;
        }

        .total-row {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .total-row td {
            border-top: 2px solid #000;
            padding: 8px 5px;
        }

        @media print {
            body {
                padding: 0;
            }

            .container {
                border: 2px solid #000;
            }

            @page {
                size: A4 landscape;
                margin: 8mm;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>DISCOVERY Living RESTO</h1>
            <div class="date-line">
                <span>.../.../...</span>
                <span style="margin-left: auto; margin-right: 100px;">FOND</span>
            </div>
        </div>

        <table class="main-table">
            <thead>
                <tr>
                    <th class="commande-col">Commande</th>
                    <th class="prix-col">Prix</th>
                    <th class="input-col">S.J</th>
                    <th style="width: 300px;">VENTE</th>
                    <th style="width: 80px;">TOTAL</th>
                    <th class="input-col">S.F</th>
                </tr>
            </thead>
            <tbody>
                <!-- CRÊPES -->
                <tr>
                    <td class="commande-col">Crêpe Thon Fromage</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe au Chocolat</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Chocolat Banane</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Chocolat Amande</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Spéciale Salé</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- TABOUNA -->
                <tr>
                    <td class="commande-col">Tabouna Thon</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Escalope</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Chawarma</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Spécial</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- MA9LOUB -->
                <tr>
                    <td class="commande-col">Ma9loub Thon</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Escalope</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Chawarma</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Farcie</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Mexicain</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- MALFOUF -->
                <tr>
                    <td class="commande-col">Malfouf Thon</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Malfouf Escalope</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Malfouf Chawarma</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- LIBANAIS -->
                <tr>
                    <td class="commande-col">Libanais Thon</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Libanais Escalope</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Libanais Chawarma</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- PIZZAS -->
                <tr>
                    <td class="commande-col">Pizza Margaritta Moyenne</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Margaritta Maxi</td>
                    <td class="prix-col">10000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Neptune Moyenne</td>
                    <td class="prix-col">10000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Neptune Maxi</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Bianca Moyenne</td>
                    <td class="prix-col">10000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Bianca Maxi</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza 4 Saisons Moyenne</td>
                    <td class="prix-col">11000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza 4 Saisons Maxi</td>
                    <td class="prix-col">13000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Escalope Moyenne</td>
                    <td class="prix-col">11000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Escalope Maxi</td>
                    <td class="prix-col">13000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Mexicain Moyenne</td>
                    <td class="prix-col">11000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Mexicain Maxi</td>
                    <td class="prix-col">13000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Turc Moyenne</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Turc Maxi</td>
                    <td class="prix-col">14000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Calzone Moyenne</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Calzone Maxi</td>
                    <td class="prix-col">14000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- BOISSONS CHAUDES -->
                <tr>
                    <td class="commande-col">Express</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Cappuccino</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Café Crème Direct</td>
                    <td class="prix-col">3000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Thé / Infusion</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- BOISSONS FROIDES -->
                <tr>
                    <td class="commande-col">Eau Minérale 1/2 Litre</td>
                    <td class="prix-col">1000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Eau Minérale 1 Litre</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Canette</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- JUS -->
                <tr>
                    <td class="commande-col">Citronnade</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Mojito</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Fraise</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- PAUSE GOURMANDE -->
                <tr>
                    <td class="commande-col">Cake</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Croissant et Pain Chocolat</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Gâteaux au Choix</td>
                    <td class="prix-col">4000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- CHICHA -->
                <tr>
                    <td class="commande-col">Chicha Jirac</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Chicha Fekher</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- TOTAL -->
                <tr class="total-row">
                    <td class="commande-col"><strong>TOTAL</strong></td>
                    <td class="prix-col"></td>
                    <td class="input-col" id="totalSJ">0</td>
                    <td></td>
                    <td class="prix-col" id="totalGeneral">0</td>
                    <td class="input-col" id="totalSF">0</td>
                </tr>
            </tbody>
        </table>
    </div>

    <script>
        // Fonction pour calculer les totaux
        function calculateTotals() {
            let totalSJ = 0;
            let totalSF = 0;
            let totalGeneral = 0;

            // Parcourir toutes les lignes sauf la ligne TOTAL
            const rows = document.querySelectorAll('tbody tr:not(.total-row)');

            rows.forEach(row => {
                const prix = parseInt(row.cells[1].textContent) || 0;
                const sjInput = row.querySelector('td:nth-child(3) input');
                const sfInput = row.querySelector('td:nth-child(6) input');
                const totalCell = row.querySelector('.total-cell');

                const sjQty = parseInt(sjInput.value) || 0;
                const sfQty = parseInt(sfInput.value) || 0;
                const totalQty = sjQty + sfQty;
                const lineTotal = prix * totalQty;

                totalCell.textContent = lineTotal;
                totalSJ += sjQty;
                totalSF += sfQty;
                totalGeneral += lineTotal;
            });

            // Mettre à jour les totaux
            document.getElementById('totalSJ').textContent = totalSJ;
            document.getElementById('totalSF').textContent = totalSF;
            document.getElementById('totalGeneral').textContent = totalGeneral;
        }

        // Ajouter des écouteurs d'événements à tous les inputs
        document.addEventListener('input', function(e) {
            if (e.target.type === 'number') {
                calculateTotals();
            }
        });

        // Calculer les totaux au chargement
        calculateTotals();
    </script>
</body>
</html>
                <!-- CRÊPES -->
                <tr>
                    <td class="commande-col">Crêpe Thon Fromage</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe au Chocolat</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Chocolat Banane</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Chocolat Amande</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Crêpe Spéciale Salé</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- TABOUNA -->
                <tr>
                    <td class="commande-col">Tabouna Thon</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Escalope</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Chawarma</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Tabouna Spécial</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- MA9LOUB -->
                <tr>
                    <td class="commande-col">Ma9loub Thon</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Escalope</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Chawarma</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Farcie</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Ma9loub Mexicain</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- MALFOUF -->
                <tr>
                    <td class="commande-col">Malfouf Thon</td>
                    <td class="prix-col">5000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Malfouf Escalope</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Malfouf Chawarma</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- LIBANAIS -->
                <tr>
                    <td class="commande-col">Libanais Thon</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Libanais Escalope</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Libanais Chawarma</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- PIZZAS -->
                <tr>
                    <td class="commande-col">Pizza Margherita</td>
                    <td class="prix-col">8000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Thon</td>
                    <td class="prix-col">10000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Escalope</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Chawarma</td>
                    <td class="prix-col">12000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Mixte</td>
                    <td class="prix-col">14000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Fruits de Mer</td>
                    <td class="prix-col">15000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Végétarienne</td>
                    <td class="prix-col">10000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza 4 Fromages</td>
                    <td class="prix-col">13000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Pizza Spéciale</td>
                    <td class="prix-col">16000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- BOISSONS CHAUDES -->
                <tr>
                    <td class="commande-col">Café Express</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Café Direct</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Cappuccino</td>
                    <td class="prix-col">3000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Café Turc</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Thé</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Chocolat Chaud</td>
                    <td class="prix-col">4000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>

                <!-- BOISSONS FROIDES -->
                <tr>
                    <td class="commande-col">Eau Minérale 1.5L</td>
                    <td class="prix-col">2000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Eau Minérale 0.5L</td>
                    <td class="prix-col">1000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Coca Cola</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Fanta</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Sprite</td>
                    <td class="prix-col">2500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Jus d'Orange</td>
                    <td class="prix-col">4000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Jus de Pomme</td>
                    <td class="prix-col">4000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Citronnade</td>
                    <td class="prix-col">3500</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Smoothie Fraise</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Smoothie Banane</td>
                    <td class="prix-col">6000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Milkshake Chocolat</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr>
                    <td class="commande-col">Milkshake Vanille</td>
                    <td class="prix-col">7000</td>
                    <td class="input-col"><input type="number" min="0"></td>
                    <td></td>
                    <td class="prix-col total-cell">0</td>
                    <td class="input-col"><input type="number" min="0"></td>
                </tr>
                <tr class="total-row">
                    <td class="commande-col"><strong>TOTAL</strong></td>
                    <td class="prix-col"></td>
                    <td class="input-col" id="totalSJ">0</td>
                    <td></td>
                    <td class="prix-col" id="totalGeneral">0</td>
                    <td class="input-col" id="totalSF">0</td>
                </tr>
            </tbody>
        </table>

        <div class="caisse-section">
            <div class="logo">
                <div>DISCOVERY</div>
            </div>
            <div>
                <div style="margin-bottom: 10px;">
                    <strong>Caisse 1:</strong>
                    <input type="number" class="caisse-input" id="caisse1" placeholder="0">
                </div>
                <div>
                    <strong>Caisse 2:</strong>
                    <input type="number" class="caisse-input" id="caisse2" placeholder="0">
                </div>
            </div>
        </div>
    </div>

    <script>
        // Fonction pour calculer les totaux
        function calculateTotals() {
            let totalSJ = 0;
            let totalSF = 0;
            let totalGeneral = 0;

            // Parcourir toutes les lignes sauf la ligne TOTAL
            const rows = document.querySelectorAll('tbody tr:not(.total-row)');

            rows.forEach(row => {
                const prix = parseInt(row.cells[1].textContent) || 0;
                const sjInput = row.querySelector('td:nth-child(3) input');
                const sfInput = row.querySelector('td:nth-child(6) input');
                const totalCell = row.querySelector('.total-cell');

                const sjQty = parseInt(sjInput.value) || 0;
                const sfQty = parseInt(sfInput.value) || 0;
                const totalQty = sjQty + sfQty;
                const lineTotal = prix * totalQty;

                totalCell.textContent = lineTotal;
                totalSJ += sjQty;
                totalSF += sfQty;
                totalGeneral += lineTotal;
            });

            // Mettre à jour les totaux
            document.getElementById('totalSJ').textContent = totalSJ;
            document.getElementById('totalSF').textContent = totalSF;
            document.getElementById('totalGeneral').textContent = totalGeneral;
        }

        // Ajouter des écouteurs d'événements à tous les inputs
        document.addEventListener('input', function(e) {
            if (e.target.type === 'number') {
                calculateTotals();
            }
        });

        // Fonction pour imprimer
        function printPage() {
            window.print();
        }

        // Fonction pour réinitialiser
        function resetAll() {
            if (confirm('Voulez-vous vraiment réinitialiser tous les champs ?')) {
                document.querySelectorAll('input[type="number"]').forEach(input => {
                    input.value = '';
                });
                calculateTotals();
            }
        }

        // Calculer les totaux au chargement
        calculateTotals();

        // Ajouter des boutons d'action
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            const buttonsDiv = document.createElement('div');
            buttonsDiv.style.cssText = 'text-align: center; margin: 20px 0; padding: 10px;';
            buttonsDiv.innerHTML = `
                <button onclick="printPage()" style="margin: 5px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer;">🖨️ Imprimer</button>
                <button onclick="resetAll()" style="margin: 5px; padding: 10px 20px; background: #dc3545; color: white; border: none; border-radius: 5px; cursor: pointer;">🔄 Réinitialiser</button>
            `;
            container.appendChild(buttonsDiv);
        });
    </script>
</body>
</html>

        <div class="content">
            <!-- Crêpes -->
            <div class="category">
                <div class="category-header">🥞 CRÊPES</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Crêpe Thon Fromage</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe au Chocolat</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Chocolat Banane</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Chocolat Amande</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Spéciale Salé</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Tabouna -->
            <div class="category">
                <div class="category-header">🫓 TABOUNA</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Tabouna Thon</td>
                            <td class="item-price">5.000</td>
                            <td><input type="number" class="qty-input" data-price="5.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Escalope</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Chawarma</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Spécial</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Ma9loub -->
            <div class="category">
                <div class="category-header">🌯 MA9LOUB</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Ma9loub Thon</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Escalope</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Chawarma</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Farcie</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Mexicain</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Malfouf -->
            <div class="category">
                <div class="category-header">🌯 MALFOUF</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Malfouf Thon</td>
                            <td class="item-price">5.000</td>
                            <td><input type="number" class="qty-input" data-price="5.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Malfouf Escalope</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Malfouf Chawarma</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Libanais -->
            <div class="category">
                <div class="category-header">🥙 LIBANAIS</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Libanais Thon</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Libanais Escalope</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Libanais Chawarma</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pizzas -->
            <div class="category">
                <div class="category-header">🍕 PIZZAS</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Pizza Margaritta Moyenne</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Margaritta Maxi</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Neptune Moyenne</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Neptune Maxi</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Bianca Moyenne</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Bianca Maxi</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza 4 Saisons Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza 4 Saisons Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Escalope Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Escalope Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Mexicain Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Mexicain Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Turc Moyenne</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Turc Maxi</td>
                            <td class="item-price">14.000</td>
                            <td><input type="number" class="qty-input" data-price="14.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Calzone Moyenne</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Calzone Maxi</td>
                            <td class="item-price">14.000</td>
                            <td><input type="number" class="qty-input" data-price="14.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Initialiser la date du jour
        document.getElementById('dateInput').value = new Date().toISOString().split('T')[0];

        // Fonction pour calculer les totaux
        function calculateTotals() {
            const qtyInputs = document.querySelectorAll('.qty-input');
            let totalArticles = 0;
            let totalRecette = 0;

            qtyInputs.forEach(input => {
                const qty = parseFloat(input.value) || 0;
                const price = parseFloat(input.dataset.price);
                const total = qty * price;
                
                // Mettre à jour le total de la ligne
                const totalCell = input.closest('tr').querySelector('.total-cell');
                totalCell.textContent = total.toFixed(3);
                
                totalArticles += qty;
                totalRecette += total;
            });

            // Mettre à jour le résumé
            updateSummary(totalArticles, totalRecette);
        }

        // Fonction pour mettre à jour le résumé
        function updateSummary(totalArticles, totalRecette) {
            const fondCaisse = parseFloat(document.getElementById('fondCaisse').value) || 0;
            const totalEnCaisse = totalRecette + fondCaisse;

            // Créer ou mettre à jour le résumé
            let summary = document.querySelector('.summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'summary';
                document.querySelector('.content').appendChild(summary);
            }

            summary.innerHTML = `
                <h2>📈 RÉCAPITULATIF JOURNALIER</h2>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">Total Articles Vendus</div>
                        <div class="summary-value">${totalArticles}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Recette du Jour</div>
                        <div class="summary-value">${totalRecette.toFixed(3)}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Fond de Caisse</div>
                        <div class="summary-value">${fondCaisse.toFixed(3)}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Total en Caisse</div>
                        <div class="summary-value">${totalEnCaisse.toFixed(3)}</div>
                    </div>
                </div>
                <div class="buttons">
                    <button class="btn btn-primary" onclick="window.print()">🖨️ Imprimer</button>
                    <button class="btn btn-success" onclick="saveData()">💾 Sauvegarder</button>
                    <button class="btn btn-warning" onclick="resetAll()">🔄 Réinitialiser</button>
                </div>
            `;
        }

        // Ajouter des écouteurs d'événements
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('qty-input') || e.target.id === 'fondCaisse') {
                calculateTotals();
            }
        });

        // Fonctions utilitaires
        function saveData() {
            const data = {
                date: document.getElementById('dateInput').value,
                fondCaisse: document.getElementById('fondCaisse').value,
                items: []
            };

            document.querySelectorAll('.qty-input').forEach(input => {
                const row = input.closest('tr');
                const itemName = row.querySelector('.item-name').textContent;
                const price = input.dataset.price;
                const qty = input.value;
                
                if (qty && qty > 0) {
                    data.items.push({
                        name: itemName,
                        price: price,
                        quantity: qty,
                        total: (parseFloat(qty) * parseFloat(price)).toFixed(3)
                    });
                }
            });

            localStorage.setItem('pointage_' + data.date, JSON.stringify(data));
            alert('Données sauvegardées avec succès !');
        }

        function resetAll() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les champs ?')) {
                document.querySelectorAll('.qty-input').forEach(input => {
                    input.value = '';
                });
                document.getElementById('fondCaisse').value = '';
                calculateTotals();
            }
        }

        // Calculer les totaux au chargement
        calculateTotals();
    </script>
</body>
</html>
