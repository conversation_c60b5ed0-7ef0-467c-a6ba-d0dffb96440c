<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pointage des Commandes - Restaurant</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
            line-height: 1.4;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .date-caisse {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            flex-wrap: wrap;
            gap: 10px;
        }

        .date-input, .caisse-input {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .date-input input, .caisse-input input {
            padding: 8px 12px;
            border: 2px solid white;
            border-radius: 5px;
            font-size: 1rem;
            background: rgba(255,255,255,0.9);
        }

        .content {
            padding: 20px;
        }

        .category {
            margin-bottom: 30px;
            border: 2px solid #ecf0f1;
            border-radius: 8px;
            overflow: hidden;
        }

        .category-header {
            background: #34495e;
            color: white;
            padding: 15px;
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
        }

        .items-table th {
            background: #ecf0f1;
            padding: 12px;
            text-align: left;
            font-weight: bold;
            color: #2c3e50;
            border-bottom: 2px solid #bdc3c7;
        }

        .items-table td {
            padding: 10px 12px;
            border-bottom: 1px solid #ecf0f1;
        }

        .items-table tr:hover {
            background-color: #f8f9fa;
        }

        .item-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .item-price {
            color: #27ae60;
            font-weight: bold;
            text-align: center;
        }

        .qty-input {
            width: 80px;
            padding: 8px;
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            text-align: center;
            font-size: 1rem;
        }

        .qty-input:focus {
            border-color: #3498db;
            outline: none;
        }

        .total-cell {
            font-weight: bold;
            color: #e74c3c;
            text-align: center;
            font-size: 1.1rem;
        }

        .summary {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 25px;
            margin-top: 30px;
            border-radius: 8px;
        }

        .summary h2 {
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .summary-item {
            background: rgba(255,255,255,0.1);
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }

        .summary-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 1.4rem;
            font-weight: bold;
        }

        .buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
            }
            
            .date-caisse {
                flex-direction: column;
                align-items: stretch;
            }
            
            .items-table {
                font-size: 0.9rem;
            }
            
            .qty-input {
                width: 60px;
            }
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border: 1px solid #ddd;
            }
            
            .buttons {
                display: none;
            }
            
            .qty-input {
                border: 1px solid #999;
                background: white;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 POINTAGE DES COMMANDES</h1>
            <div class="date-caisse">
                <div class="date-input">
                    <label>📅 Date:</label>
                    <input type="date" id="dateInput" />
                </div>
                <div class="caisse-input">
                    <label>💰 Fond de Caisse:</label>
                    <input type="number" id="fondCaisse" placeholder="0.000" step="0.001" />
                </div>
            </div>
        </div>

        <div class="content">
            <!-- Crêpes -->
            <div class="category">
                <div class="category-header">🥞 CRÊPES</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Crêpe Thon Fromage</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe au Chocolat</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Chocolat Banane</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Chocolat Amande</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Crêpe Spéciale Salé</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Tabouna -->
            <div class="category">
                <div class="category-header">🫓 TABOUNA</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Tabouna Thon</td>
                            <td class="item-price">5.000</td>
                            <td><input type="number" class="qty-input" data-price="5.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Escalope</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Chawarma</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Tabouna Spécial</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Ma9loub -->
            <div class="category">
                <div class="category-header">🌯 MA9LOUB</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Ma9loub Thon</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Escalope</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Chawarma</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Farcie</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Ma9loub Mexicain</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Malfouf -->
            <div class="category">
                <div class="category-header">🌯 MALFOUF</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Malfouf Thon</td>
                            <td class="item-price">5.000</td>
                            <td><input type="number" class="qty-input" data-price="5.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Malfouf Escalope</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Malfouf Chawarma</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Libanais -->
            <div class="category">
                <div class="category-header">🥙 LIBANAIS</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Libanais Thon</td>
                            <td class="item-price">6.000</td>
                            <td><input type="number" class="qty-input" data-price="6.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Libanais Escalope</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Libanais Chawarma</td>
                            <td class="item-price">7.000</td>
                            <td><input type="number" class="qty-input" data-price="7.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Pizzas -->
            <div class="category">
                <div class="category-header">🍕 PIZZAS</div>
                <table class="items-table">
                    <thead>
                        <tr>
                            <th>Article</th>
                            <th>Prix</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="item-name">Pizza Margaritta Moyenne</td>
                            <td class="item-price">8.000</td>
                            <td><input type="number" class="qty-input" data-price="8.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Margaritta Maxi</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Neptune Moyenne</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Neptune Maxi</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Bianca Moyenne</td>
                            <td class="item-price">10.000</td>
                            <td><input type="number" class="qty-input" data-price="10.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Bianca Maxi</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza 4 Saisons Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza 4 Saisons Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Escalope Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Escalope Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Mexicain Moyenne</td>
                            <td class="item-price">11.000</td>
                            <td><input type="number" class="qty-input" data-price="11.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Mexicain Maxi</td>
                            <td class="item-price">13.000</td>
                            <td><input type="number" class="qty-input" data-price="13.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Turc Moyenne</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Turc Maxi</td>
                            <td class="item-price">14.000</td>
                            <td><input type="number" class="qty-input" data-price="14.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Calzone Moyenne</td>
                            <td class="item-price">12.000</td>
                            <td><input type="number" class="qty-input" data-price="12.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                        <tr>
                            <td class="item-name">Pizza Calzone Maxi</td>
                            <td class="item-price">14.000</td>
                            <td><input type="number" class="qty-input" data-price="14.000" min="0" /></td>
                            <td class="total-cell">0.000</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Initialiser la date du jour
        document.getElementById('dateInput').value = new Date().toISOString().split('T')[0];

        // Fonction pour calculer les totaux
        function calculateTotals() {
            const qtyInputs = document.querySelectorAll('.qty-input');
            let totalArticles = 0;
            let totalRecette = 0;

            qtyInputs.forEach(input => {
                const qty = parseFloat(input.value) || 0;
                const price = parseFloat(input.dataset.price);
                const total = qty * price;
                
                // Mettre à jour le total de la ligne
                const totalCell = input.closest('tr').querySelector('.total-cell');
                totalCell.textContent = total.toFixed(3);
                
                totalArticles += qty;
                totalRecette += total;
            });

            // Mettre à jour le résumé
            updateSummary(totalArticles, totalRecette);
        }

        // Fonction pour mettre à jour le résumé
        function updateSummary(totalArticles, totalRecette) {
            const fondCaisse = parseFloat(document.getElementById('fondCaisse').value) || 0;
            const totalEnCaisse = totalRecette + fondCaisse;

            // Créer ou mettre à jour le résumé
            let summary = document.querySelector('.summary');
            if (!summary) {
                summary = document.createElement('div');
                summary.className = 'summary';
                document.querySelector('.content').appendChild(summary);
            }

            summary.innerHTML = `
                <h2>📈 RÉCAPITULATIF JOURNALIER</h2>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">Total Articles Vendus</div>
                        <div class="summary-value">${totalArticles}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Recette du Jour</div>
                        <div class="summary-value">${totalRecette.toFixed(3)}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Fond de Caisse</div>
                        <div class="summary-value">${fondCaisse.toFixed(3)}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">Total en Caisse</div>
                        <div class="summary-value">${totalEnCaisse.toFixed(3)}</div>
                    </div>
                </div>
                <div class="buttons">
                    <button class="btn btn-primary" onclick="window.print()">🖨️ Imprimer</button>
                    <button class="btn btn-success" onclick="saveData()">💾 Sauvegarder</button>
                    <button class="btn btn-warning" onclick="resetAll()">🔄 Réinitialiser</button>
                </div>
            `;
        }

        // Ajouter des écouteurs d'événements
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('qty-input') || e.target.id === 'fondCaisse') {
                calculateTotals();
            }
        });

        // Fonctions utilitaires
        function saveData() {
            const data = {
                date: document.getElementById('dateInput').value,
                fondCaisse: document.getElementById('fondCaisse').value,
                items: []
            };

            document.querySelectorAll('.qty-input').forEach(input => {
                const row = input.closest('tr');
                const itemName = row.querySelector('.item-name').textContent;
                const price = input.dataset.price;
                const qty = input.value;
                
                if (qty && qty > 0) {
                    data.items.push({
                        name: itemName,
                        price: price,
                        quantity: qty,
                        total: (parseFloat(qty) * parseFloat(price)).toFixed(3)
                    });
                }
            });

            localStorage.setItem('pointage_' + data.date, JSON.stringify(data));
            alert('Données sauvegardées avec succès !');
        }

        function resetAll() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les champs ?')) {
                document.querySelectorAll('.qty-input').forEach(input => {
                    input.value = '';
                });
                document.getElementById('fondCaisse').value = '';
                calculateTotals();
            }
        }

        // Calculer les totaux au chargement
        calculateTotals();
    </script>
</body>
</html>
