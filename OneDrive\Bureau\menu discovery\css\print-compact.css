/* ===================================
   PRINT COMPACT - Force chaque groupe sur une page
   ===================================
   
   CSS ultra-compact pour garantir que:
   - Ma9loub + Malfouf + Libanais = 1 page
   - Toutes les pizzas = 1 page
   
   ================================= */

@media print {
  /* Configuration de page normale - portrait */
  @page {
    size: A5 portrait;
    margin: 8mm !important;
  }

  body {
    font-size: 10pt !important;
    line-height: 1.3 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Groupes forcés sur des pages séparées - format original */
  .menu-section-group {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
    padding: 12mm !important;
    margin: 0 !important;
    border: 2px solid #e74c3c !important;
    border-radius: 8px !important;
    height: auto !important;
    max-height: none !important;
  }
  
  /* Force chaque groupe sur une nouvelle page */
  .menu-section-group:nth-child(1) {
    page-break-after: always !important;
  }
  
  .menu-section-group:nth-child(2) {
    page-break-before: always !important;
    page-break-after: always !important;
  }
  
  .menu-section-group:nth-child(3) {
    page-break-before: always !important;
  }
  
  /* Header - légèrement réduit */
  .menu-header {
    margin-bottom: 15pt !important;
    padding-bottom: 10pt !important;
    border-bottom: 3px solid #e74c3c !important;
  }

  .menu-title {
    font-size: 18pt !important;
    margin-bottom: 6pt !important;
    font-weight: 700 !important;
  }

  .header-decoration {
    height: 2px !important;
    width: 50px !important;
    margin: 6pt auto 0 !important;
  }

  /* Sections - légèrement réduit */
  .section-header {
    font-size: 13pt !important;
    margin-bottom: 6pt !important;
    margin-top: 12pt !important;
    padding: 5pt 0 !important;
    border-bottom: 2px solid #ecf0f1 !important;
  }

  .section-header:first-child {
    margin-top: 0 !important;
  }

  .section-header::after {
    width: 30px !important;
    height: 2px !important;
  }

  /* Menu items - légèrement réduit */
  .menu-items {
    margin-bottom: 12pt !important;
  }

  .menu-item {
    padding: 2pt 0 !important;
    margin-bottom: 1pt !important;
    font-size: 10pt !important;
    border-bottom: 1px dotted #bdc3c7 !important;
  }

  .item-name {
    font-size: 10pt !important;
    font-weight: 500 !important;
  }

  .item-price {
    font-size: 10pt !important;
    font-weight: 600 !important;
  }
  
  /* Pizza items - légèrement réduit */
  .pizza-item {
    padding: 4pt 0 !important;
    margin-bottom: 4pt !important;
  }

  .pizza-header {
    margin-bottom: 2pt !important;
  }

  .pizza-name {
    font-size: 11pt !important;
    margin-bottom: 1pt !important;
    font-weight: 600 !important;
  }

  .pizza-description {
    font-size: 8pt !important;
    margin-bottom: 2pt !important;
    line-height: 1.2 !important;
  }

  .pizza-prices {
    gap: 8pt !important;
    font-size: 9pt !important;
  }

  .pizza-size {
    font-size: 9pt !important;
  }

  .pizza-size strong {
    font-size: 9pt !important;
    font-weight: 600 !important;
  }
  
  /* Suppression des éléments inutiles */
  .menu-footer,
  .no-print {
    display: none !important;
  }
  
  /* Optimisations d'espace supplémentaires */
  .menu-container {
    padding: 0 !important;
    margin: 0 !important;
  }
  
  .menu-content {
    margin: 0 !important;
    padding: 0 !important;
  }
  
  /* Règles strictes pour éviter les débordements */
  * {
    box-sizing: border-box !important;
  }
  
  /* Force les éléments à rester ensemble */
  .menu-section {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
  
  .menu-item,
  .pizza-item {
    page-break-inside: avoid !important;
    break-inside: avoid !important;
  }
  
  /* Ajustements spécifiques pour optimiser l'espace */

  /* Groupe 2: Ma9loub + Malfouf + Libanais - plus compact */
  .menu-section-group:nth-child(2) .section-header {
    margin-top: 8pt !important;
    margin-bottom: 4pt !important;
  }

  .menu-section-group:nth-child(2) .menu-item {
    padding: 1.5pt 0 !important;
    margin-bottom: 0.5pt !important;
  }

  .menu-section-group:nth-child(2) .menu-items {
    margin-bottom: 8pt !important;
  }

  /* Groupe 3: Pizzas - plus compact */
  .menu-section-group:nth-child(3) .pizza-item {
    padding: 3pt 0 !important;
    margin-bottom: 3pt !important;
  }

  .menu-section-group:nth-child(3) .section-header {
    margin-bottom: 4pt !important;
    margin-top: 8pt !important;
  }

  .menu-section-group:nth-child(3) .pizza-header {
    margin-bottom: 1pt !important;
  }

  .menu-section-group:nth-child(3) .pizza-name {
    margin-bottom: 0.5pt !important;
  }

  .menu-section-group:nth-child(3) .pizza-description {
    margin-bottom: 1pt !important;
  }
}
